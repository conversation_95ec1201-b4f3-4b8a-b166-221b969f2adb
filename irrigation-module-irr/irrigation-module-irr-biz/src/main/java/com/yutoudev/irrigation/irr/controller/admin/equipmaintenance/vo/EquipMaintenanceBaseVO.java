package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备维护 Base VO
 *
 * <AUTHOR>
 * @description 设备维护 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-10-27 10:33:13
 */
@Data
public class EquipMaintenanceBaseVO {

    /**
     * 设备ID
     */
    private Long equipId;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 维护内容
     */
    private String description;

    /**
     * 维护结果
     */
    private String result;

    /**
     * 维护人员ID
     */
    private Long userId;

    /**
     * 维护日期
     */
    private LocalDateTime maintenanceDate;
}
