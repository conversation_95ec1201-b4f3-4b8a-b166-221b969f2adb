package com.yutoudev.irrigation.irr.service.coef;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.irr.controller.admin.coef.vo.submerged.*;
import com.yutoudev.irrigation.irr.convert.coef.CoefSubmergedConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefSubmergedDO;
import com.yutoudev.irrigation.irr.dal.mysql.coef.CoefSubmergedMapper;
import com.yutoudev.irrigation.irr.dal.redis.RedisKeyConstants;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.ErrorCodeConstants.*;

/**
 * 淹没系数Service实现类
 *
 * <AUTHOR>
 * @description 管理后台-淹没系数Service实现类
 * @time 2024-08-05 23:32:42
 */
@Service
@Validated
public class CoefSubmergedServiceImpl extends TalonsServiceImpl<CoefSubmergedMapper, CoefSubmergedDO> implements CoefSubmergedService<CoefSubmergedDO> {

    @Resource
    private CoefSubmergedMapper coefSubmergedMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void checkField(CoefSubmergedDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(CoefSubmergedCreateReqVO createReqVO) {
        // 清除缓存
        clearListCache();
        // 插入
        CoefSubmergedDO coefSubmerged = CoefSubmergedConvert.INSTANCE.convert(createReqVO);
        this.save(coefSubmerged, true);
        // 返回
        return coefSubmerged.getId();
    }

    @Override
    public boolean createBatch(List<CoefSubmergedCreateReqVO> list) {
        // 清除缓存
        clearListCache();

        List<CoefSubmergedDO> saveList = CoefSubmergedConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        } else {
            throw exception(COEF_SUBMERGED_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public void update(CoefSubmergedUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateCoefSubmergedExists(updateReqVO.getId());
        // 清除缓存
        clearListCache();
        // 更新
        CoefSubmergedDO updateObj = CoefSubmergedConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    public boolean updateBatch(List<CoefSubmergedUpdateReqVO> list) {

        List<CoefSubmergedDO> updateList = CoefSubmergedConvert.INSTANCE.convertUpdateBatch(list);

        for (CoefSubmergedDO tempDO : updateList) {
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateCoefSubmergedExists(tempDO.getId());
        }

        // 清除缓存
        clearListCache();

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(COEF_SUBMERGED_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateCoefSubmergedExists(id);
        // 清除缓存
        clearListCache();
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        // 清除缓存
        clearListCache();
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(COEF_SUBMERGED_DELETE_BATCH_ERROR);
        }
    }

    private void validateCoefSubmergedExists(Long id) {
        if (coefSubmergedMapper.selectById(id) == null) {
            throw exception(COEF_SUBMERGED_NOT_EXISTS);
        }
    }

    @Override
    public CoefSubmergedDO get(Long id) {
        return this.getById(id, true);
    }

    @Override
    public double getCoef(Double waterLevelRate) {
        LambdaQueryWrapper<CoefSubmergedDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CoefSubmergedDO::getWaterLevelRate, waterLevelRate);
        CoefSubmergedDO coefSubmergedDO = baseMapper.selectOne(queryWrapper);
        if (Objects.nonNull(coefSubmergedDO)) {
            return coefSubmergedDO.getCoef();
        }
        return 0;
    }

    @Override
    public List<CoefSubmergedDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public PageResult<CoefSubmergedDO> page(CoefSubmergedPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<CoefSubmergedDO> queryBuilder = new QueryCriteriaWrapperBuilder<CoefSubmergedDO>() {
        };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<CoefSubmergedDO> pageResult = coefSubmergedMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
        talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<CoefSubmergedDO> getList(CoefSubmergedQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<CoefSubmergedDO> queryBuilder = new QueryCriteriaWrapperBuilder<CoefSubmergedDO>() {
        };
        queryBuilder.build(queryReqVO);

        List<CoefSubmergedDO> result = coefSubmergedMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<CoefSubmergedDO> getListByCache() {
        String key = RedisKeyConstants.COEF_SUBMERGED_LIST;
        List<CoefSubmergedDO> cacheList = (List<CoefSubmergedDO>) redisTemplate.opsForValue().get(key);
        if (CollectionUtils.isEmpty(cacheList)) {
            List<CoefSubmergedDO> list = this.list();
            if (CollectionUtils.isEmpty(list)) {
                return List.of();
            }
            cacheList = list;
            redisTemplate.opsForValue().set(key, cacheList);
        }
        return cacheList;
    }

    @Override
    public void clearListCache() {
        redisTemplate.delete(RedisKeyConstants.COEF_SUBMERGED_LIST);
    }

    @Override
    public ImportExcelRespVO importExcel(List<CoefSubmergedExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(COEF_SUBMERGED_IMPORT_LIST_IS_EMPTY);
        }

        // 清除缓存
        clearListCache();

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<CoefSubmergedDO> saveList = CoefSubmergedConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            CoefSubmergedDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, CoefSubmergedExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}
