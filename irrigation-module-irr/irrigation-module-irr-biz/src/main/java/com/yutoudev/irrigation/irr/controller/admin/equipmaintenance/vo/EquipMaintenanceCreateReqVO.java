package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo;

import com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo.EquipMaintenanceFileCreateReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;


/**
 * 设备维护创建RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护创建RequestVO
 * @time 2024-10-27 10:33:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EquipMaintenanceCreateReqVO extends EquipMaintenanceBaseVO {

    /**
     * 文件列表
     */
    private List<EquipMaintenanceFileCreateReqVO> files;
}
