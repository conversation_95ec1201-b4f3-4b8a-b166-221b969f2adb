package com.yutoudev.irrigation.irr.controller.admin.coef;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.coef.vo.submerged.*;
import com.yutoudev.irrigation.irr.convert.coef.CoefSubmergedConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefSubmergedDO;
import com.yutoudev.irrigation.irr.service.coef.CoefSubmergedService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 淹没系数
 *
 * <AUTHOR>
 * @description 管理后台-淹没系数controller
 * @time 2024-08-05 23:32:42
 */
@RestController
@RequestMapping("/irr/coef-submerged")
@Validated
public class CoefSubmergedController {

    private static final String MODULE_NAME = "淹没系数";

    @Resource
    private CoefSubmergedService<CoefSubmergedDO> coefSubmergedService;

    /**
     * 创建淹没系数
     *
     * @param createReqVO CoefSubmergedCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody CoefSubmergedCreateReqVO createReqVO) {
        return success(coefSubmergedService.create(createReqVO));
    }

    /**
     * 批量创建淹没系数
     *
     * @param lists CoefSubmergedCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<CoefSubmergedCreateReqVO> lists) {
        return success(coefSubmergedService.createBatch(lists));
    }

    /**
     * 更新淹没系数
     *
     * @param updateReqVO CoefSubmergedUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody CoefSubmergedUpdateReqVO updateReqVO) {
        coefSubmergedService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新淹没系数
     *
     * @param lists 批量更新列表 CoefSubmergedUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<CoefSubmergedUpdateReqVO> lists) {
        return success(coefSubmergedService.updateBatch(lists));
    }

    /**
     * 删除淹没系数
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        coefSubmergedService.delete(id);
        return success(true);
    }

    /**
     * 批量删除淹没系数
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(coefSubmergedService.deleteBatch(ids));
    }

    /**
     * 获得淹没系数详情
     *
     * @param id 编号 Long
     * @return CommonResult<CoefSubmergedDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasAnyPermissions('irr:coef-submerged:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<CoefSubmergedDetailRespVO> get(@RequestParam("id") Long id) {
        CoefSubmergedDO coefSubmerged = coefSubmergedService.get(id);
        return success(CoefSubmergedConvert.INSTANCE.convertDetail(coefSubmerged));
    }

    /**
     * 淹没系数列表
     *
     * @param queryReqVO 查询条件 CoefSubmergedQueryReqVO
     * @return CommonResult<List < CoefSubmergedRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasAnyPermissions('irr:coef-submerged:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<CoefSubmergedRespVO>> getList(@RequestQueryParam CoefSubmergedQueryReqVO queryReqVO) {
        List<CoefSubmergedDO> list = coefSubmergedService.getList(queryReqVO);
        return success(CoefSubmergedConvert.INSTANCE.convertList(list));
    }

//    @GetMapping("/list-cache")
//    @PreAuthorize("@ss.hasAnyPermissions('irr:coef-submerged:query','common:query')")
//    public CommonResult<List<CoefSubmergedRespVO>> getListByCache() {
//        List<CoefSubmergedDO> list = coefSubmergedService.getListByCache();
//        return success(CoefSubmergedConvert.INSTANCE.convertList(list));
//    }

    /**
     * 淹没系数分页
     *
     * @param pageVO 查询条件 CoefSubmergedPageReqVO
     * @return CommonResult<PageResult < CoefSubmergedRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasAnyPermissions('irr:coef-submerged:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<CoefSubmergedRespVO>> page(@RequestQueryParam CoefSubmergedPageReqVO pageVO) {
        PageResult<CoefSubmergedDO> pageResult = coefSubmergedService.page(pageVO);
        return success(CoefSubmergedConvert.INSTANCE.convertPage(pageResult));
    }

//    @DeleteMapping("/clear-cache")
//    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:update')")
//    public CommonResult<Boolean> clearCache() {
//        coefSubmergedService.clearListCache();
//        return success(true);
//    }

    /**
     * 导出淹没系数Excel
     *
     * @param queryReqVO 查询条件 CoefSubmergedExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam CoefSubmergedExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<CoefSubmergedDO> list = coefSubmergedService.getList(queryReqVO);
        // 导出 Excel
        List<CoefSubmergedExcelVO> datas = CoefSubmergedConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "淹没系数", "xlsx"), queryReqVO.getExportSheetName(),
                CoefSubmergedExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入淹没系数模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "淹没系数-导入模版.xls", "sheet1", CoefSubmergedExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入淹没系数Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:coef-submerged:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<CoefSubmergedExcelVO> list = ExcelUtils.read(file, CoefSubmergedExcelVO.class);
        return success(coefSubmergedService.importExcel(list, isUpdate));
    }
}