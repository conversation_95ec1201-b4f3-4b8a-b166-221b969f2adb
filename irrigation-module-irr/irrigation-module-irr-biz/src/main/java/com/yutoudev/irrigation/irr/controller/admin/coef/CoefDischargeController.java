package com.yutoudev.irrigation.irr.controller.admin.coef;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.coef.vo.discharge.*;
import com.yutoudev.irrigation.irr.convert.coef.CoefDischargeConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefDischargeDO;
import com.yutoudev.irrigation.irr.service.coef.CoefDischargeService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 流量系数
 *
 * <AUTHOR>
 * @description 管理后台-流量系数controller
 * @time 2024-08-05 23:32:43
 */
@RestController
@RequestMapping("/irr/coef-discharge")
@Validated
public class CoefDischargeController {

    private static final String MODULE_NAME = "流量系数";

    @Resource
    private CoefDischargeService<CoefDischargeDO> coefDischargeService;

    /**
     * 创建流量系数
     *
     * @param createReqVO CoefDischargeCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody CoefDischargeCreateReqVO createReqVO) {
        return success(coefDischargeService.create(createReqVO));
    }

    /**
     * 批量创建流量系数
     *
     * @param lists CoefDischargeCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<CoefDischargeCreateReqVO> lists) {
        return success(coefDischargeService.createBatch(lists));
    }

    /**
     * 更新流量系数
     *
     * @param updateReqVO CoefDischargeUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody CoefDischargeUpdateReqVO updateReqVO) {
        coefDischargeService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新流量系数
     *
     * @param lists 批量更新列表 CoefDischargeUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<CoefDischargeUpdateReqVO> lists) {
        return success(coefDischargeService.updateBatch(lists));
    }

    /**
     * 删除流量系数
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        coefDischargeService.delete(id);
        return success(true);
    }

    /**
     * 批量删除流量系数
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(coefDischargeService.deleteBatch(ids));
    }

    /**
     * 获得流量系数详情
     *
     * @param id 编号 Long
     * @return CommonResult<CoefDischargeDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasAnyPermissions('irr:coef-discharge:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<CoefDischargeDetailRespVO> get(@RequestParam("id") Long id) {
        CoefDischargeDO coefDischarge = coefDischargeService.get(id);
        return success(CoefDischargeConvert.INSTANCE.convertDetail(coefDischarge));
    }

    /**
     * 流量系数列表
     *
     * @param queryReqVO 查询条件 CoefDischargeQueryReqVO
     * @return CommonResult<List < CoefDischargeRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasAnyPermissions('irr:coef-discharge:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<CoefDischargeRespVO>> getList(@RequestQueryParam CoefDischargeQueryReqVO queryReqVO) {
        List<CoefDischargeDO> list = coefDischargeService.getList(queryReqVO);
        return success(CoefDischargeConvert.INSTANCE.convertList(list));
    }

    /**
    @GetMapping("/list-cache")
    @PreAuthorize("@ss.hasAnyPermissions('irr:coef-discharge:query','common:query')")
    public CommonResult<List<CoefDischargeRespVO>> getListByCache() {
        List<CoefDischargeDO> list = coefDischargeService.getListByCache();
        return success(CoefDischargeConvert.INSTANCE.convertList(list));
    }
    */

    /**
     * 流量系数分页
     *
     * @param pageVO 查询条件 CoefDischargePageReqVO
     * @return CommonResult<PageResult < CoefDischargeRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasAnyPermissions('irr:coef-discharge:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<CoefDischargeRespVO>> page(@RequestQueryParam CoefDischargePageReqVO pageVO) {
        PageResult<CoefDischargeDO> pageResult = coefDischargeService.page(pageVO);
        return success(CoefDischargeConvert.INSTANCE.convertPage(pageResult));
    }

//    @DeleteMapping("/clear-cache")
//    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:update')")
//    public CommonResult<Boolean> clearCache() {
//        coefDischargeService.clearListCache();
//        return success(true);
//    }

    /**
     * 导出流量系数Excel
     *
     * @param queryReqVO 查询条件 CoefDischargeExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam CoefExportReqVODischarge queryReqVO, HttpServletResponse response) throws IOException {
        List<CoefDischargeDO> list = coefDischargeService.getList(queryReqVO);
        // 导出 Excel
        List<CoefDischargeExcelVO> datas = CoefDischargeConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "流量系数", "xlsx"), queryReqVO.getExportSheetName(),
                CoefDischargeExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入流量系数模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "流量系数-导入模版.xls", "sheet1", CoefDischargeExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入流量系数Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:coef-discharge:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<CoefDischargeExcelVO> list = ExcelUtils.read(file, CoefDischargeExcelVO.class);
        return success(coefDischargeService.importExcel(list, isUpdate));
    }
}