package com.yutoudev.irrigation.irr.controller.large.statistics.vo;

import lombok.Data;
import lombok.ToString;

/**
 * 用水类型用水汇总
 *
 * <AUTHOR>
 * @description 大屏接口-用水类型用水汇总
 * @time 2025-03-12
 */
@Data
@ToString(callSuper = true)
public class UseTypeWaterVO {

    private Integer id;

    /**
     * 用水类型名称，该名称与 用水类型 irr_user_use_type 对应的值
     */
    private String name;

    /**
     * 累计用水量 m³
     */
    private Double accumulate;


}
