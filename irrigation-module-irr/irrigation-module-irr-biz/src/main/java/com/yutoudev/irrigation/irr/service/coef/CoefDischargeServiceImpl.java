package com.yutoudev.irrigation.irr.service.coef;

import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.irr.controller.admin.coef.vo.discharge.*;
import com.yutoudev.irrigation.irr.convert.coef.CoefDischargeConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefDischargeDO;
import com.yutoudev.irrigation.irr.dal.mysql.coef.CoefDischargeMapper;
import com.yutoudev.irrigation.irr.dal.redis.RedisKeyConstants;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.ErrorCodeConstants.*;

/**
 * 流量系数Service实现类
 *
 * <AUTHOR>
 * @description 管理后台-流量系数Service实现类
 * @time 2024-08-05 23:32:43
 */
@Service
@Validated
public class CoefDischargeServiceImpl extends TalonsServiceImpl<CoefDischargeMapper, CoefDischargeDO> implements CoefDischargeService<CoefDischargeDO> {

    @Resource
    private CoefDischargeMapper coefDischargeMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void checkField(CoefDischargeDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(CoefDischargeCreateReqVO createReqVO) {
        // 清除缓存
        clearListCache();
        // 插入
        CoefDischargeDO coefDischarge = CoefDischargeConvert.INSTANCE.convert(createReqVO);
        this.save(coefDischarge, true);
        // 返回
        return coefDischarge.getId();
    }

    @Override
    public boolean createBatch(List<CoefDischargeCreateReqVO> list) {
        // 清除缓存
        clearListCache();

        List<CoefDischargeDO> saveList = CoefDischargeConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        } else {
            throw exception(COEF_DISCHARGE_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public void update(CoefDischargeUpdateReqVO updateReqVO) {;
        // 校验存在
        this.validateCoefDischargeExists(updateReqVO.getId());
        // 清除缓存
        clearListCache();
        // 更新
        CoefDischargeDO updateObj = CoefDischargeConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    public boolean updateBatch(List<CoefDischargeUpdateReqVO> list) {

        List<CoefDischargeDO> updateList = CoefDischargeConvert.INSTANCE.convertUpdateBatch(list);

        for (CoefDischargeDO tempDO : updateList) {
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateCoefDischargeExists(tempDO.getId());
        }

        // 清除缓存
        clearListCache();

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(COEF_DISCHARGE_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateCoefDischargeExists(id);
        // 清除缓存
        clearListCache();
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        // 清除缓存
        clearListCache();
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(COEF_DISCHARGE_DELETE_BATCH_ERROR);
        }
    }

    private void validateCoefDischargeExists(Long id) {
        if (coefDischargeMapper.selectById(id) == null) {
            throw exception(COEF_DISCHARGE_NOT_EXISTS);
        }
    }

    @Override
    public CoefDischargeDO get(Long id) {
        return this.getById(id, true);
    }

    @Override
    public CoefDischargeDO getCoef(Integer gateType, Integer gateGroup, Integer flowPattern, Integer wallType) {
        LambdaQueryWrapperX<CoefDischargeDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(CoefDischargeDO::getGateType, gateType);
        queryWrapperX.eq(CoefDischargeDO::getGateGroup, gateGroup);
        queryWrapperX.eq(CoefDischargeDO::getFlowPattern, flowPattern);
        queryWrapperX.eqIfPresent(CoefDischargeDO::getWallType, wallType);
        return baseMapper.selectOne(queryWrapperX);
    }

    @Override
    public CoefDischargeDO getCoef(String name) {
        LambdaQueryWrapperX<CoefDischargeDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(CoefDischargeDO::getName, name);
        return baseMapper.selectOne(queryWrapperX);
    }

    @Override
    public List<CoefDischargeDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public PageResult<CoefDischargeDO> page(CoefDischargePageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<CoefDischargeDO> queryBuilder = new QueryCriteriaWrapperBuilder<CoefDischargeDO>() {
        };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<CoefDischargeDO> pageResult = coefDischargeMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
        talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<CoefDischargeDO> getList(CoefDischargeQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<CoefDischargeDO> queryBuilder = new QueryCriteriaWrapperBuilder<CoefDischargeDO>() {
        };
        queryBuilder.build(queryReqVO);

        List<CoefDischargeDO> result = coefDischargeMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<CoefDischargeDO> getListByCache() {
        String key = RedisKeyConstants.COEF_DISCHARGE_LIST;
        List<CoefDischargeDO> cacheList = (List<CoefDischargeDO>) redisTemplate.opsForValue().get(key);
        if (CollectionUtils.isEmpty(cacheList)) {
            List<CoefDischargeDO> list = this.list();
            if (CollectionUtils.isEmpty(list)) {
                return List.of();
            }
            cacheList = list;
            redisTemplate.opsForValue().set(key, cacheList);
        }
        return cacheList;
    }

    @Override
    public void clearListCache() {
        redisTemplate.delete(RedisKeyConstants.COEF_DISCHARGE_LIST);
    }

    @Override
    public ImportExcelRespVO importExcel(List<CoefDischargeExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(COEF_DISCHARGE_IMPORT_LIST_IS_EMPTY);
        }

        // 清除缓存
        clearListCache();

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<CoefDischargeDO> saveList = CoefDischargeConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            CoefDischargeDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, CoefDischargeExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}
