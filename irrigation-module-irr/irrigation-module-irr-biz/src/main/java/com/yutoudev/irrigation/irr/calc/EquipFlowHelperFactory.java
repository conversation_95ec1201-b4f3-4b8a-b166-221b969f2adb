package com.yutoudev.irrigation.irr.calc;

import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipsection.EquipSectionDO;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.equipsection.EquipSectionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * EquipFlowHelper 工厂类
 * 
 * 提供线程安全的方式创建和使用 EquipFlowHelper 实例
 * 
 * <AUTHOR>
 */
@Component
public class EquipFlowHelperFactory {

    @Resource
    private EquipSectionService<EquipSectionDO> equipSectionService;

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    /**
     * 创建并初始化 EquipFlowHelper 实例
     * 
     * @param equipId 设备ID
     * @param height 水深
     * @return 初始化后的 EquipFlowHelper 实例
     */
    public EquipFlowHelper createHelper(Long equipId, Double height) {
        EquipSectionDO equipSection = equipSectionService.getById(equipId, false);
        EquipBaseCacheVO equip = equipBaseService.getCacheById(equipId);
        if (Objects.nonNull(equip.getReportDataOffset())) {
            height = height - equip.getReportDataOffset();
        }
        return createHelper(equipSection, height);
    }

    /**
     * 创建并初始化 EquipFlowHelper 实例
     * 
     * @param equipSection 设备断面信息
     * @param height 水深
     * @return 初始化后的 EquipFlowHelper 实例
     */
    public EquipFlowHelper createHelper(EquipSectionDO equipSection, Double height) {
        EquipFlowHelper helper = new EquipFlowHelper();
        helper.setEquipSectionService(equipSectionService);
        helper.setEquipBaseService(equipBaseService);
        helper.init(equipSection, height);
        return helper;
    }

    /**
     * 执行流量计算操作
     * 
     * @param equipId 设备ID
     * @param height 水深
     * @param calculator 计算函数
     * @param <T> 返回类型
     * @return 计算结果
     */
    public <T> T calculate(Long equipId, Double height, FlowCalculator<T> calculator) {
        EquipFlowHelper helper = createHelper(equipId, height);
        return calculator.calculate(helper);
    }

    /**
     * 执行流量计算操作
     * 
     * @param equipSection 设备断面信息
     * @param height 水深
     * @param calculator 计算函数
     * @param <T> 返回类型
     * @return 计算结果
     */
    public <T> T calculate(EquipSectionDO equipSection, Double height, FlowCalculator<T> calculator) {
        EquipFlowHelper helper = createHelper(equipSection, height);
        return calculator.calculate(helper);
    }

    /**
     * 流量计算函数式接口
     * 
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface FlowCalculator<T> {
        T calculate(EquipFlowHelper helper);
    }
}
