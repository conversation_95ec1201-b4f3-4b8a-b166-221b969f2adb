package com.yutoudev.irrigation.irr.controller.large.chan;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterEfficiencyRespVO;
import com.yutoudev.irrigation.irr.controller.large.chan.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.chanbase.ChanBaseDO;
import com.yutoudev.irrigation.irr.service.chanbase.ChanBaseService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * 大屏 - 水渠统计
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/chan-base")
@Validated
@Slf4j
public class LargeChanBaseController {

    private static final String MODULE_NAME = "大屏-水渠统计";

    @Resource
    private ChanBaseService<ChanBaseDO> chanBaseService;

    /**
     * 获取水渠衬砌长度列表
     *
     * @return 水渠衬砌长度统计列表
     */
    @GetMapping("/page-lining")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<PageResult<ChanLiningRespVO>> pageLining(@RequestQueryParam ChanBasePageReqVO pageReqVO) {
        PageResult<ChanLiningRespVO> list = chanBaseService.pageLining(pageReqVO);
        return success(list);
    }

    /**
     * 获取水渠衬砌长度列表
     *
     * @return 水渠衬砌长度统计列表
     */
    @GetMapping("/lining-stats")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<ChanLiningStatsRespVO> statisticsLining() {
        return success(chanBaseService.statisticsLining());
    }

    /**
     * 查询水渠评价情况统计明细
     *
     * @return 查询水渠评价情况统计明细
     */
    @GetMapping("/page-situation")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<PageResult<ChanSituationRespVO>> pageSituation(@RequestQueryParam ChanBasePageReqVO pageReqVO) {
        PageResult<ChanSituationRespVO> list = chanBaseService.pageSituation(pageReqVO);
        return success(list);
    }

    /**
     * 查询水渠评价情况统计
     *
     * @return 查询水渠评价情况统计
     */
    @GetMapping("/situation-stats")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<ChanSituationStatsRespVO> statisticsSituation() {
        return success(chanBaseService.statisticsSituation());
    }

    /**
     * 获取水渠灌溉效率
     *
     * @return 水渠灌溉效率
     */
    @GetMapping("/water-efficiency")
    public CommonResult<WaterEfficiencyLargeVO> getWaterEfficiency(){
        return success(chanBaseService.getWaterEfficiency());
    }
}