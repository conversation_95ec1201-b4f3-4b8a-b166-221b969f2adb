package com.yutoudev.irrigation.irr.controller.admin.waterflowconfig.vo.config;

import lombok.*;
import java.util.*;

/**
 *
 * 水流配置Excel导出RequestVO
 * @description 管理后台-水流配置Excel导出RequestVO
 * <AUTHOR>
 * @time 2024-08-13 15:15:33
 *
 */
@Data
@Builder
public class WaterFlowConfigExportReqVO extends  WaterFlowConfigQueryReqVO {

		/**
		 * 标题名称
		 * @description 若不特别说明，缺省为sheet1
		 */
		private String exportSheetName = "sheet1";

		/**
		 * 导出文件名
		 * @description 导出文件名称，若未设置则根据默认规则（实体中文名称+导出时间）产生文件名
		 */
		private String exportFileName;

		/**
		 * 导出指定列
		 * @description 字符串数组
		 * @mock ['name','age']
		 */
		private List<String> exportIncludeColumns;

		/**
		 * 排除指定列
		 * @description 字符串数组
		 * @mock ['createTime','age']
		 */
		private List<String> exportExcludeColumns;

}
