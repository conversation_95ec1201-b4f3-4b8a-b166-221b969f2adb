package com.yutoudev.irrigation.irr.service.coef;

import java.util.*;
import javax.validation.*;

import com.yutoudev.irrigation.irr.controller.admin.coef.vo.submerged.*;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefDischargeDO;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefSubmergedDO;
import io.github.portaldalaran.talons.core.ITalonsService;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;

/**
 *
 * 淹没系数Service接口
 * @description 管理后台-淹没系数Service接口，对ITalonsService的扩展
 * <AUTHOR>
 * @time 2024-08-05 23:32:42
 *
 */
public interface CoefSubmergedService<T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建淹没系数
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid CoefSubmergedCreateReqVO createReqVO);

    /**
    * 批量创建淹没系数
    *
    * @param list 创建信息
    * @return 编号
    */
    boolean createBatch(@Valid List<CoefSubmergedCreateReqVO> list);

    /**
     * 更新淹没系数
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid CoefSubmergedUpdateReqVO updateReqVO);

    /**
    * 批量更新淹没系数
    *
    * @param list 更新信息
    */
    boolean updateBatch(@Valid List<CoefSubmergedUpdateReqVO> list);

    /**
     * 删除淹没系数
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
    * 批量删除淹没系数
    *
    * @param ids 编号
    */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得淹没系数
     *
     * @param id 编号
     * @return 淹没系数
     */
    CoefSubmergedDO get(Long id);

    /**
     * 获得淹没系数
     * @param waterLevelRate
     * @return
     */
    double getCoef(Double waterLevelRate);
    /**
     * 淹没系数列表
     *
     * @param ids 编号
     * @return 淹没系数列表
     */
    List<CoefSubmergedDO> getList(List<Long> ids);

    /**
     * 淹没系数分页
     *
     * @param pageReqVO 分页查询
     * @return 淹没系数分页
     */
    PageResult<CoefSubmergedDO> page(CoefSubmergedPageReqVO pageReqVO);

    /**
    * 获得淹没系数列表,
    *
    * @param queryReqVO 查询条件键值对
    * @return 淹没系数列表
    */
    List<CoefSubmergedDO> getList(CoefSubmergedQueryReqVO queryReqVO);

    /**
     * 获取所有淹没系数缓存
     *
     * @return 流量系数列表
     */
    List<CoefSubmergedDO> getListByCache();

    /**
     * 清理所有淹没系数缓存
     */
    void clearListCache();

    /**
    * 批量导入淹没系数 excel
    *
    * @param importList     导入淹没系数列表
    * @param isUpdateSupport 是否支持更新
    * @return 导入结果
    */
    ImportExcelRespVO importExcel(List<CoefSubmergedExcelVO> importList, boolean isUpdateSupport);
}