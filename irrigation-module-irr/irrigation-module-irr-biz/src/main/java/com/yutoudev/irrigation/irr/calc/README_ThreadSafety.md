# EquipFlowHelper 线程安全解决方案

## 问题描述

原始的 `EquipFlowHelper` 类存在线程安全问题：
- 作为 Spring 单例 Bean，包含可变的实例变量 `section` 和 `flowHelper`
- `init()` 方法会修改实例状态，在多线程环境下会导致数据竞争
- 计算方法依赖实例状态，可能得到错误的计算结果

## 解决方案

### 方案1：原型作用域（推荐）

将 `EquipFlowHelper` 改为原型作用域，每次注入时创建新实例。

**优点：**
- 代码改动最小
- 完全线程安全
- 使用方式与原来基本一致

**缺点：**
- 每次使用都创建新实例，略有性能开销

**使用方式：**
```java
@Service
public class SomeService {
    
    @Resource
    private ApplicationContext applicationContext;
    
    public void someMethod() {
        // 每次获取新实例
        EquipFlowHelper helper = applicationContext.getBean(EquipFlowHelper.class);
        helper.init(equipId, height);
        Double flow = helper.getFlow(waterSpeed);
    }
}
```

### 方案2：工厂模式

使用 `EquipFlowHelperFactory` 创建和管理实例。

**优点：**
- 提供更清晰的API
- 支持函数式编程风格
- 完全线程安全

**使用方式：**
```java
@Service
public class SomeService {
    
    @Resource
    private EquipFlowHelperFactory equipFlowHelperFactory;
    
    public void someMethod() {
        // 方式1：创建实例
        EquipFlowHelper helper = equipFlowHelperFactory.createHelper(equipId, height);
        Double flow = helper.getFlow(waterSpeed);
        
        // 方式2：函数式计算
        Double flow2 = equipFlowHelperFactory.calculate(equipId, height, 
            helper -> helper.getFlow(waterSpeed));
    }
}
```

### 方案3：ThreadLocal

使用 `ThreadSafeEquipFlowHelper` 为每个线程维护独立状态。

**优点：**
- 同一线程内可以复用状态
- 性能较好
- API与原来类似

**缺点：**
- 需要手动管理生命周期
- 容易造成内存泄漏

**使用方式：**
```java
@Service
public class SomeService {
    
    @Resource
    private ThreadSafeEquipFlowHelper threadSafeHelper;
    
    public void someMethod() {
        // 方式1：手动管理
        try {
            threadSafeHelper.init(equipId, height);
            Double flow = threadSafeHelper.getFlow(waterSpeed);
        } finally {
            threadSafeHelper.clear(); // 重要：避免内存泄漏
        }
        
        // 方式2：自动管理（推荐）
        Double flow2 = threadSafeHelper.calculate(equipId, height, 
            helper -> helper.getFlow(waterSpeed));
    }
}
```

## 迁移建议

### 立即迁移（推荐）

1. **使用原型作用域**：已经修改了 `EquipFlowHelper` 为原型作用域
2. **更新注入方式**：将 `@Resource` 改为通过 `ApplicationContext` 获取

### 渐进式迁移

1. **新代码使用工厂模式**：`EquipFlowHelperFactory`
2. **旧代码保持不变**：继续使用原型作用域的 `EquipFlowHelper`
3. **逐步重构**：将旧代码迁移到工厂模式

## 性能对比

| 方案 | 内存使用 | CPU开销 | 线程安全 | 易用性 |
|------|----------|---------|----------|--------|
| 原型作用域 | 中等 | 低 | ✅ | 高 |
| 工厂模式 | 中等 | 低 | ✅ | 高 |
| ThreadLocal | 低 | 最低 | ✅ | 中等 |

## 注意事项

1. **原型作用域**：每次注入都创建新实例，注意获取方式
2. **工厂模式**：推荐使用函数式API，代码更简洁
3. **ThreadLocal**：必须调用 `clear()` 方法，避免内存泄漏
4. **测试**：确保在多线程环境下进行充分测试

## 示例代码更新

原来的代码：
```java
@Resource
private EquipFlowHelper equipFlowHelper;

public void calculate() {
    equipFlowHelper.init(equipId, height);
    Double flow = equipFlowHelper.getFlow(waterSpeed);
}
```

更新后的代码（工厂模式）：
```java
@Resource
private EquipFlowHelperFactory equipFlowHelperFactory;

public void calculate() {
    Double flow = equipFlowHelperFactory.calculate(equipId, height, 
        helper -> helper.getFlow(waterSpeed));
}
```
