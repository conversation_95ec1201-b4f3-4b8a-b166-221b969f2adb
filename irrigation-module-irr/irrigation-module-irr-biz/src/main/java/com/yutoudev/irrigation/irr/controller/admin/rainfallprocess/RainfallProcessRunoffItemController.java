package com.yutoudev.irrigation.irr.controller.admin.rainfallprocess;

import com.google.common.base.Joiner;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.rainfallprocess.vo.runoffitem.*;
import com.yutoudev.irrigation.irr.convert.rainfallprocess.RainfallProcessRunoffItemConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess.RainfallProcessRunoffItemDO;
import com.yutoudev.irrigation.irr.service.rainfallprocess.RainfallProcessRunoffItemService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 流域降雨过程净雨
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/rainfall-process-runoff-item")
@Validated
public class RainfallProcessRunoffItemController {

    private static final String MODULE_NAME = "流域降雨过程净雨";

    @Resource
    private RainfallProcessRunoffItemService<RainfallProcessRunoffItemDO> rainfallProcessRunoffItemService;

    /**
     * 创建流域降雨过程净雨
     *
     * @param createReqVO RainfallProcessRunoffItemCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody RainfallProcessRunoffItemCreateReqVO createReqVO) {
        return success(rainfallProcessRunoffItemService.create(createReqVO));
    }

    /**
     * 批量创建流域降雨过程净雨
     *
     * @param lists RainfallProcessRunoffItemCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<RainfallProcessRunoffItemCreateReqVO> lists) {
        return success(rainfallProcessRunoffItemService.createBatch(lists));
    }

    /**
     * 更新流域降雨过程净雨
     *
     * @param updateReqVO RainfallProcessRunoffItemUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody RainfallProcessRunoffItemUpdateReqVO updateReqVO) {
        rainfallProcessRunoffItemService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新流域降雨过程净雨
     *
     * @param lists 批量更新列表 RainfallProcessRunoffItemUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<RainfallProcessRunoffItemUpdateReqVO> lists) {
        return success(rainfallProcessRunoffItemService.updateBatch(lists));
    }

    /**
     * 删除流域降雨过程净雨
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        rainfallProcessRunoffItemService.delete(id);
        return success(true);
    }

    /**
     * 批量删除流域降雨过程净雨
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(rainfallProcessRunoffItemService.deleteBatch(ids));
    }

    /**
     * 获得流域降雨过程净雨详情
     *
     * @param id 编号 Long
     * @return CommonResult<RainfallProcessRunoffItemDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<RainfallProcessRunoffItemDetailRespVO> get(@RequestParam("id") Long id) {
        RainfallProcessRunoffItemDO rainfallProcessRunoffItem = rainfallProcessRunoffItemService.get(id);
        return success(RainfallProcessRunoffItemConvert.INSTANCE.convertDetail(rainfallProcessRunoffItem));
    }

    /**
     * 流域降雨过程净雨列表
     *
     * @param queryReqVO 查询条件 RainfallProcessRunoffItemQueryReqVO
     * @return CommonResult<List < RainfallProcessRunoffItemRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasAnyPermissions('irr:rainfall-process-runoff:query','common.query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<RainfallProcessRunoffItemRespVO>> getList(@RequestQueryParam RainfallProcessRunoffItemQueryReqVO queryReqVO) {
        List<RainfallProcessRunoffItemDO> list = rainfallProcessRunoffItemService.getList(queryReqVO);
        return success(RainfallProcessRunoffItemConvert.INSTANCE.convertList(list));
    }

    /**
     * 流域降雨过程净雨分页
     *
     * @param pageVO 查询条件 RainfallProcessRunoffItemPageReqVO
     * @return CommonResult<PageResult < RainfallProcessRunoffItemRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasAnyPermissions('irr:rainfall-process-runoff:query','common.query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<RainfallProcessRunoffItemRespVO>> page(@RequestQueryParam RainfallProcessRunoffItemPageReqVO pageVO) {
        PageResult<RainfallProcessRunoffItemDO> pageResult = rainfallProcessRunoffItemService.page(pageVO);
        return success(RainfallProcessRunoffItemConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出流域降雨过程净雨Excel
     *
     * @param queryReqVO 查询条件 RainfallProcessRunoffItemExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam RainfallProcessRunoffItemExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<RainfallProcessRunoffItemDO> list = rainfallProcessRunoffItemService.getList(queryReqVO);
        // 导出 Excel
        List<RainfallProcessRunoffItemExcelVO> datas = RainfallProcessRunoffItemConvert.INSTANCE.convertExportExcel(list);
        for (RainfallProcessRunoffItemExcelVO data : datas) {
            if (Objects.nonNull(data.getParamO()) && !data.getParamO().isEmpty()) {
                data.setParamOStr(Joiner.on(",").join(data.getParamO()));
            }
        }
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "流域降雨过程净雨", "xlsx"), queryReqVO.getExportSheetName(),
                RainfallProcessRunoffItemExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入流域降雨过程净雨模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "流域降雨过程净雨-导入模版.xls", "sheet1", RainfallProcessRunoffItemExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入流域降雨过程净雨Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:rainfall-process-runoff:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<RainfallProcessRunoffItemExcelVO> list = ExcelUtils.read(file, RainfallProcessRunoffItemExcelVO.class);
        return success(rainfallProcessRunoffItemService.importExcel(list, isUpdate));
    }
}