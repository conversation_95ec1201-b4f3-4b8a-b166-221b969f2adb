package com.yutoudev.irrigation.irr.controller.large.regimen;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.controller.admin.equipcalcconfig.vo.EquipCalcConfigCacheVO;
import com.yutoudev.irrigation.irr.controller.large.regimen.vo.MainWaterSourceRespVO;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefLevelCapacityDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipcalcconfig.EquipCalcConfigDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndMinuteDO;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import com.yutoudev.irrigation.irr.service.coef.CoefLevelCapacityService;
import com.yutoudev.irrigation.irr.service.equipcalcconfig.EquipCalcConfigService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndHourService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndMinuteService;
import com.yutoudev.irrigation.irr.service.swhsbase.SwhsBaseService;
import com.yutoudev.irrigation.irr.service.waterdatacalc.ReservoirDataCalcService;
import com.yutoudev.irrigation.module.infra.api.config.ConfigApi;
import com.yutoudev.irrigation.module.infra.api.config.dto.ApiConfigRespDTO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;
import static com.yutoudev.irrigation.irr.enums.IrrigationGlobalConstants.PROVINCE_REPORT_RAINFALL_DEV_INFO;

/**
 * 主水源水库水情
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/main-water-source")
@Validated
public class LargeMainWaterSourceController {

    private static final String MODULE_NAME = "大屏-主水源水情";

    @Resource
    private EquipCalcConfigService<EquipCalcConfigDO> equipCalcConfigService;

    @Resource
    private ReservoirDataCalcService reservoirDataCalcService;

    @Resource
    private SwhsBaseService<SwhsBaseDO> swhsBaseService;

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndHourService;

    @Resource
    private CoefLevelCapacityService<CoefLevelCapacityDO> coefLevelCapacityService;

    @Resource
    private MeasIndMinuteService<MeasIndMinuteDO> measIndMinuteService;

    @Resource
    private ConfigApi configApi;

    @GetMapping("/get-regimen")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.STATISTIC, type = GET)
    public CommonResult<MainWaterSourceRespVO> getRegimen() {

        EquipCalcConfigCacheVO config = equipCalcConfigService.getCacheMainWaterSourceConfig();
        SwhsBaseDO mainWaterSource = swhsBaseService.getById(config.getWaterSourceId());

        EquipBaseCacheVO equip = reservoirDataCalcService.getMainWaterSourceEquip();

        MeasIndHourDO data = measIndHourService.getCurrentDataByEquipAndTime(equip.getCentralId(), equip.getDevId(),
                LocalDateTime.now().withMinute(0).withSecond(0).withNano(0));

        MainWaterSourceRespVO vo = new MainWaterSourceRespVO();
        MeasIndHourDO dataNullable = Optional.ofNullable(data).orElseGet(MeasIndHourDO::new);
        vo.setRealTimeWaterLevel(Objects.isNull(dataNullable.getWaterLevel()) ? 0f : dataNullable.getWaterLevel());
        vo.setRealTimeStorage(Objects.isNull(dataNullable.getWaterLevel()) ? 0f
                : coefLevelCapacityService.getStorageCapacity(dataNullable.getEquipId(),
                BigDecimal.valueOf(dataNullable.getWaterLevel()).setScale(3, RoundingMode.HALF_UP).doubleValue()).floatValue());
        vo.setOutflow(Objects.isNull(dataNullable.getReservoirOutflow()) ? 0f : dataNullable.getReservoirOutflow());
        vo.setInflow(Objects.isNull(dataNullable.getReservoirInflow()) ? 0f : dataNullable.getReservoirInflow());
        vo.setCheckFloodLevel(Objects.isNull(mainWaterSource.getFloodCheckLevel()) ? 0f : (mainWaterSource.getFloodCheckLevel()).floatValue());
        vo.setFloodM8Level(Objects.isNull(mainWaterSource.getFloodM8Level()) ? 0f : mainWaterSource.getFloodM8Level().floatValue());
        vo.setFloodM4Level(Objects.isNull(mainWaterSource.getFloodM4Level()) ? 0f : mainWaterSource.getFloodM4Level().floatValue());
        return success(vo);
    }


    /**
     * 大坝降雨
     * @return
     */
    @GetMapping("/dam-rainfall")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<String> damRainfall() {

        ApiConfigRespDTO rainfallDevInfo = configApi.getConfigByKey(PROVINCE_REPORT_RAINFALL_DEV_INFO);
        String[] deviceInfo = rainfallDevInfo.getValue().split(",");

        MeasIndMinuteDO measIndMinute = measIndMinuteService.getLastDataByEquipId(deviceInfo[0], deviceInfo[1]);
        String rainText = "";
        if (Objects.nonNull(measIndMinute)) {
            if (measIndMinute.getRainfall() > 0.6) {
                rainText = "大雨";
            } else if (measIndMinute.getRainfall() > 0.3) {
                rainText = "中雨";
            } else if (measIndMinute.getRainfall() > 0) {
                rainText = "小雨";
            }
        }
        return success(rainText);
    }

}
