# EquipFlowHelper 线程安全迁移指南

## 当前问题

现有代码中 `EquipFlowHelper` 的使用方式存在线程安全问题：

```java
@Resource
private EquipFlowHelper equipFlowHelper;

public void someMethod() {
    equipFlowHelper.init(equipId, height);
    Double result = equipFlowHelper.getSectionArea();
}
```

## 迁移方案

### 方案A：使用 ApplicationContext（最小改动）

适用于不想大幅修改现有代码的情况：

```java
@Resource
private ApplicationContext applicationContext;

public void someMethod() {
    // 获取新的原型实例
    EquipFlowHelper equipFlowHelper = applicationContext.getBean(EquipFlowHelper.class);
    equipFlowHelper.init(equipId, height);
    Double result = equipFlowHelper.getSectionArea();
}
```

### 方案B：使用工厂模式（推荐）

提供更清晰的API和更好的性能：

```java
@Resource
private EquipFlowHelperFactory equipFlowHelperFactory;

public void someMethod() {
    // 方式1：创建实例
    EquipFlowHelper helper = equipFlowHelperFactory.createHelper(equipId, height);
    Double result = helper.getSectionArea();
    
    // 方式2：函数式计算（推荐）
    Double result2 = equipFlowHelperFactory.calculate(equipId, height, 
        helper -> helper.getSectionArea());
}
```

### 方案C：使用 ThreadLocal

适用于需要在同一线程中多次使用的场景：

```java
@Resource
private ThreadSafeEquipFlowHelper threadSafeHelper;

public void someMethod() {
    // 自动管理生命周期（推荐）
    Double result = threadSafeHelper.calculate(equipId, height, 
        helper -> helper.getSectionArea());
}
```

## 具体文件迁移示例

### 1. WaterSpeedStrategy.java

**原代码：**
```java
@Resource
private EquipFlowHelper equipFlowHelper;

// 在某个方法中
equipFlowHelper.init(equipId, height);
Double flow = equipFlowHelper.getFlow(waterSpeed);
```

**迁移后（工厂模式）：**
```java
@Resource
private EquipFlowHelperFactory equipFlowHelperFactory;

// 在某个方法中
Double flow = equipFlowHelperFactory.calculate(equipId, height, 
    helper -> helper.getFlow(waterSpeed));
```

### 2. WaterDataCalcServiceImpl.java

**原代码：**
```java
@Resource
private EquipFlowHelper equipFlowHelper;

public double calculateFlow(Long equipId, Double height, Double waterSpeed) {
    equipFlowHelper.init(equipId, height);
    return equipFlowHelper.getFlow(waterSpeed);
}
```

**迁移后（工厂模式）：**
```java
@Resource
private EquipFlowHelperFactory equipFlowHelperFactory;

public double calculateFlow(Long equipId, Double height, Double waterSpeed) {
    return equipFlowHelperFactory.calculate(equipId, height, 
        helper -> helper.getFlow(waterSpeed));
}
```

### 3. EquipControlDisplayServiceImpl.java

**原代码：**
```java
// 在异步任务中使用
CalcFlowHelper calcFlowHelper = new CalcFlowHelper();
Section section = new Section();
BeanUtils.copyProperties(resp.getSection(), section);
section.setHeight(resp.getV15().doubleValue());
calcFlowHelper.init(section);
Double sectionArea = calcFlowHelper.getSectionArea();
```

**迁移后（直接使用）：**
```java
// 这种情况下直接创建新实例即可，已经是线程安全的
CalcFlowHelper calcFlowHelper = new CalcFlowHelper();
Section section = new Section();
BeanUtils.copyProperties(resp.getSection(), section);
section.setHeight(resp.getV15().doubleValue());
calcFlowHelper.init(section);
Double sectionArea = calcFlowHelper.getSectionArea();
```

## 迁移步骤

### 第一步：选择迁移方案

1. **简单快速**：选择方案A（ApplicationContext）
2. **长期维护**：选择方案B（工厂模式）
3. **高性能要求**：选择方案C（ThreadLocal）

### 第二步：更新依赖注入

将现有的 `@Resource private EquipFlowHelper equipFlowHelper;` 替换为对应的方案。

### 第三步：更新使用代码

根据选择的方案更新所有使用 `equipFlowHelper` 的地方。

### 第四步：测试验证

1. **单元测试**：确保计算结果正确
2. **并发测试**：验证多线程环境下的安全性
3. **性能测试**：确保性能满足要求

## 注意事项

1. **渐进式迁移**：可以先迁移部分代码，逐步完成全部迁移
2. **测试覆盖**：每个迁移的方法都应该有对应的测试
3. **性能监控**：关注迁移后的性能变化
4. **代码审查**：确保所有团队成员理解新的使用方式

## 常见问题

### Q: 为什么不直接加锁？
A: 加锁会严重影响性能，而且容易出现死锁问题。使用无状态或线程本地状态是更好的选择。

### Q: 原型作用域会影响性能吗？
A: 创建对象的开销很小，相比线程安全问题带来的风险，这点性能开销是可以接受的。

### Q: 如何确保迁移后的代码是线程安全的？
A: 通过编写并发测试，模拟多线程环境下的使用场景，验证结果的正确性。

## 推荐的迁移优先级

1. **高并发场景**：优先迁移
2. **异步任务**：优先迁移  
3. **定时任务**：优先迁移
4. **普通业务逻辑**：按计划迁移
