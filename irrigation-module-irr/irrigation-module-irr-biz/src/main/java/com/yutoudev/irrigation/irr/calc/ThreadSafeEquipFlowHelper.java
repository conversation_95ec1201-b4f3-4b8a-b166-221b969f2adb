package com.yutoudev.irrigation.irr.calc;

import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipsection.EquipSectionDO;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.equipsection.EquipSectionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 线程安全的流量计算工具类
 * 
 * 使用 ThreadLocal 确保每个线程都有独立的计算状态
 * 
 * <AUTHOR>
 */
@Component
public class ThreadSafeEquipFlowHelper {

    @Resource
    private EquipSectionService<EquipSectionDO> equipSectionService;

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    /**
     * 线程本地存储的计算状态
     */
    private static final ThreadLocal<CalculationState> THREAD_LOCAL_STATE = new ThreadLocal<>();

    /**
     * 计算状态内部类
     */
    private static class CalculationState {
        private Section section;
        private CalcFlowHelper flowHelper;
    }

    /**
     * 初始化计算状态
     * 
     * @param equipId 设备ID
     * @param height 水深
     */
    public void init(Long equipId, Double height) {
        EquipSectionDO equipSection = equipSectionService.getById(equipId, false);
        EquipBaseCacheVO equip = equipBaseService.getCacheById(equipId);
        if (Objects.nonNull(equip.getReportDataOffset())) {
            height = height - equip.getReportDataOffset();
        }
        init(equipSection, height);
    }

    /**
     * 初始化计算状态
     * 
     * @param equipSection 设备断面信息
     * @param height 水深
     */
    public void init(EquipSectionDO equipSection, Double height) {
        CalculationState state = new CalculationState();
        state.section = SectionConvert.INSTANCE.convertEquip(equipSection);
        state.section.setHeight(height);
        state.flowHelper = new CalcFlowHelper();
        state.flowHelper.init(state.section);
        THREAD_LOCAL_STATE.set(state);
    }

    /**
     * 清理线程本地状态（重要：避免内存泄漏）
     */
    public void clear() {
        THREAD_LOCAL_STATE.remove();
    }

    /**
     * 获取当前线程的计算状态
     */
    private CalculationState getState() {
        CalculationState state = THREAD_LOCAL_STATE.get();
        if (state == null) {
            throw new IllegalStateException("请先调用 init() 方法初始化计算状态");
        }
        return state;
    }

    /**
     * 返回断面面积
     */
    public Double getSectionArea() {
        return getState().flowHelper.getSectionArea();
    }

    /**
     * 返回湿周
     */
    public Double getWettedPerimeter() {
        return getState().flowHelper.getWettedPerimeter();
    }

    /**
     * 水力半径
     */
    public Double getHydraulicRadius() {
        return getSectionArea() / getWettedPerimeter();
    }

    /**
     * 流速
     */
    public Double getVelocityOfFlow() {
        return getState().flowHelper.getVelocityOfFlow();
    }

    /**
     * 获取流量
     */
    public Double getFlow(Double waterSpeed) {
        return getState().flowHelper.getFlow(waterSpeed);
    }

    /**
     * 根据流量计算流速
     */
    public Double getSpeedByFlow(Double flow) {
        CalculationState state = getState();
        if (Objects.isNull(state.section)) {
            return 0.0d;
        }
        double sectionArea = getSectionArea();
        if (sectionArea == 0) {
            return 0.0d;
        }
        return BigDecimal.valueOf(flow / sectionArea).setScale(3, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 执行计算操作（自动管理状态生命周期）
     * 
     * @param equipId 设备ID
     * @param height 水深
     * @param calculator 计算函数
     * @param <T> 返回类型
     * @return 计算结果
     */
    public <T> T calculate(Long equipId, Double height, FlowCalculator<T> calculator) {
        try {
            init(equipId, height);
            return calculator.calculate(this);
        } finally {
            clear();
        }
    }

    /**
     * 执行计算操作（自动管理状态生命周期）
     * 
     * @param equipSection 设备断面信息
     * @param height 水深
     * @param calculator 计算函数
     * @param <T> 返回类型
     * @return 计算结果
     */
    public <T> T calculate(EquipSectionDO equipSection, Double height, FlowCalculator<T> calculator) {
        try {
            init(equipSection, height);
            return calculator.calculate(this);
        } finally {
            clear();
        }
    }

    /**
     * 流量计算函数式接口
     */
    @FunctionalInterface
    public interface FlowCalculator<T> {
        T calculate(ThreadSafeEquipFlowHelper helper);
    }
}
