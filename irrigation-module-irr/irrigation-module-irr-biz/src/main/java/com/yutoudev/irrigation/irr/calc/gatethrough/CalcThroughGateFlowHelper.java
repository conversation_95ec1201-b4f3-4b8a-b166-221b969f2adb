package com.yutoudev.irrigation.irr.calc.gatethrough;

import com.yutoudev.irrigation.framework.common.util.object.BeanUtils;
import com.yutoudev.irrigation.irr.calc.SluiceGateHelper;
import com.yutoudev.irrigation.irr.calc.coefficient.CoefficientDischarge;
import com.yutoudev.irrigation.irr.calc.coefficient.CoefficientSubmerge;
import com.yutoudev.irrigation.irr.calc.sluice.SluiceGate;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefDischargeDO;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefSubmergedDO;
import com.yutoudev.irrigation.irr.service.coef.CoefDischargeService;
import com.yutoudev.irrigation.irr.service.coef.CoefSubmergedService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CalcThroughGateFlowHelper {

    @Resource
    private CoefDischargeService<CoefDischargeDO> coefDischargeService;

    @Resource
    private CoefSubmergedService<CoefSubmergedDO> coefSubmergedService;

    public double calculate(double beforeWaterDepth, double afterWaterDepth, double gateOpening) {
        SluiceGateHelper sluiceGateHelper = new SluiceGateHelper();
        SluiceGate sluiceGate = new SluiceGate();
        List<CoefficientSubmerge> coefficientSubmergeList = new ArrayList<>();
        List<CoefficientDischarge> coefficientDischargeList = new ArrayList<>();

        List<CoefDischargeDO> coefDischargeList = coefDischargeService.list();
        for (CoefDischargeDO coefDischarge : coefDischargeList) {
            CoefficientDischarge coefficientDischarge = BeanUtils.toBean(coefDischarge, CoefficientDischarge.class);
            coefficientDischarge.setCoefficient(coefDischarge.getCoef());
            coefficientDischargeList.add(coefficientDischarge);
        }
        List<CoefSubmergedDO> coefSubmergedList = coefSubmergedService.list();
        for (CoefSubmergedDO coefSubmerged : coefSubmergedList) {
            CoefficientSubmerge coefficientSubmerge = BeanUtils.toBean(coefSubmerged, CoefficientSubmerge.class);
            coefficientSubmergeList.add(coefficientSubmerge);
        }

        double flow = 0;

        if (beforeWaterDepth > 0) {
            try {
                sluiceGateHelper.init(sluiceGate, afterWaterDepth, beforeWaterDepth, coefficientSubmergeList, coefficientDischargeList);
                flow = sluiceGateHelper.getFlow(gateOpening);
            } catch (Exception e) {
                log.error("过闸计算流量异常", e);
            }
        }
        return flow;
    }
}
