package com.yutoudev.irrigation.irr.controller.large.warn;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.warn.api.warn.WarnApi;
import com.yutoudev.irrigation.warn.api.warn.dto.WarnInfoFlickerDTO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.DELETE;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 预警信息闪烁
 *
 * <AUTHOR>
 * @description 管理后台-预警信息controller
 * @time 2024-06-26 14:24:22
 */
@RestController
@RequestMapping("/ew/warn")
@Validated
public class WarnController {

    private static final String MODULE_NAME = "预警信息闪烁";

    @Resource
    private WarnApi warnApi;

    /**
     * 预警信息闪烁列表
     *
     * @return CommonResult<List < WarnInfoFlickerRespVO>> 列表响应VO
     * @description 查询预警信息闪烁列表
     */
    @GetMapping("/list")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<WarnInfoFlickerDTO>> getList(@RequestParam("warnType") String warnType, @RequestParam(name = "regulationType", required = false) String regulationType) {
        return success(warnApi.getFlickList(warnType, regulationType));
    }

    /**
     * 删除预警信息闪烁
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        warnApi.delete(id);
        return success(true);
    }
}