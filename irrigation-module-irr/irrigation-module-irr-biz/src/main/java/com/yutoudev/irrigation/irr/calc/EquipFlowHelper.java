package com.yutoudev.irrigation.irr.calc;

import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipsection.EquipSectionDO;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.equipsection.EquipSectionService;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 计算流量工具类
 *
 * 注意：此类使用原型作用域以确保线程安全，每次注入都会创建新实例
 *
 * <AUTHOR>
 */
@Data
@Component
@Scope("prototype")
public class EquipFlowHelper {

    private Section section;

    private CalcFlowHelper flowHelper;

    @Resource
    private EquipSectionService<EquipSectionDO> equipSectionService;

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    /**
     * 设置设备断面服务（用于工厂模式）
     */
    public void setEquipSectionService(EquipSectionService<EquipSectionDO> equipSectionService) {
        this.equipSectionService = equipSectionService;
    }

    /**
     * 设置设备基础服务（用于工厂模式）
     */
    public void setEquipBaseService(EquipBaseService<EquipBaseDO> equipBaseService) {
        this.equipBaseService = equipBaseService;
    }

    public void init(Long equipId, Double height) {
        EquipSectionDO equipSection = equipSectionService.getById(equipId, false);
        EquipBaseCacheVO equip = equipBaseService.getCacheById(equipId);
        if (Objects.nonNull(equip.getReportDataOffset())) {
            height = height - equip.getReportDataOffset();
        }
        init(equipSection, height);
    }

    public void init(EquipSectionDO equipSection, Double height) {

        this.section = SectionConvert.INSTANCE.convertEquip(equipSection);
        section.setHeight(height);
        flowHelper = new CalcFlowHelper();
        flowHelper.init(section);
    }

    /**
     * 返回断面面积
     *
     * @return 断面面积
     * @apiNote 若断面设置为空，则返回0
     */
    public Double getSectionArea() {
        return flowHelper.getSectionArea();
    }

    /**
     * 返回湿周
     *
     * @return
     */
    public Double getWettedPerimeter() {
        return flowHelper.getWettedPerimeter();
    }

    /**
     * 水力半径
     *
     * @return
     */
    public Double getHydraulicRadius() {
        //过水面积/湿周
        return getSectionArea() / getWettedPerimeter();
    }

    /**
     * 流速
     *
     * @return
     */
    public Double getVelocityOfFlow() {
        return getFlowHelper().getVelocityOfFlow();
    }

    /**
     * 获取流量
     *
     * @param waterSpeed 流速
     * @return 流量
     * @apiNote 1、若流速不传，可根据工式计算流速，这时必须有设置糙率、水力坡度。
     * 2、若不传流速，未设备糙率、水力坡度，则返回流量不可信
     * 3、若断面为空则返回0
     */
    public Double getFlow(Double waterSpeed) {
        return getFlowHelper().getFlow(waterSpeed);
    }

    /**
     * 根据流量计算流速
     *
     * @param flow 流量
     * @return 流速
     */
    public Double getSpeedByFlow(Double flow) {
        if (Objects.isNull(section)) {
            return 0.0d;
        }
        double sectionArea = getSectionArea();
        // 当断面为0时
        if (sectionArea == 0) {
            return 0.0d;
        }
        return BigDecimal.valueOf(flow / sectionArea).setScale(3, RoundingMode.HALF_UP).doubleValue();
    }
}
