package com.yutoudev.irrigation.irr.controller.admin.equipmaintenance.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.yutoudev.irrigation.framework.excel.core.annotations.DictFormat;
import com.yutoudev.irrigation.framework.excel.core.convert.DictConvert;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 设备维护ExcelVO
 *
 * <AUTHOR>
 * @description 管理后台-设备维护导出、导入ExcelVO
 * @time 2024-10-27 10:33:13
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class EquipMaintenanceExcelVO {


    /**
     * ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 设备名称
     */
    @ExcelProperty("设备名称")
    private Long equipName;

    /**
     * 中心站ID
     */
    @ExcelProperty("中心站ID")
    private String centralId;

    /**
     * 遥测站ID
     */
    @ExcelProperty("遥测站ID")
    private String devId;

    /**
     * 类型
     *
     * @description 使用了DictConvert，根据irr_equip_maintenance_type定义，确定该值范围
     */
    @ExcelProperty(value = "类型", converter = DictConvert.class)
    // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    @DictFormat("irr_equip_maintenance_type")
    private Integer type;

    /**
     * 维护内容
     */
    @ExcelProperty("维护内容")
    private String description;

    /**
     * 维护结果
     */
    @ExcelProperty("维护结果")
    private String result;

    /**
     * 维护人员
     */
    @ExcelProperty("维护人员")
    private Long userName;

    /**
     * 维护日期
     */
    @ExcelProperty("维护日期")
    private LocalDateTime maintenanceDate;
}
