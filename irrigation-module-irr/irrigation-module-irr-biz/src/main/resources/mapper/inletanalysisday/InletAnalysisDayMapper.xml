<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.inletanalysisday.InletAnalysisDayMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="sumRainfallGroupBySwhsId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.inletanalysis.InletAnalysisDO">
        SELECT swhs_id,swhs_name,SUM(rainfall) rainfall,evaporation,penetration,`day` statistics_date FROM `irr_inlet_analysis_day` WHERE  chan_id is NULL AND `day`=#{day} GROUP BY swhs_id
    </select>


    <select id="sumRainfallGroupByChanId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.inletanalysis.InletAnalysisDO">
        SELECT chan_id,chan_name,SUM(rainfall) rainfall,evaporation,penetration,`day` statistics_date FROM `irr_inlet_analysis_day` WHERE chan_id is NOT NULL AND `day`=#{day} GROUP BY chan_id
    </select>

    <select id="sumRainfallGroupByCatchmentIdAndMonth" resultType="java.lang.Double">
        SELECT SUM(rainfall) from irr_inlet_analysis_day WHERE catchment_id=#{catchmentId} AND `day` LIKE CONCAT(#{month},'%')
    </select>
    <select id="sumRainfallVolumeGroupByCatchmentIdAndMonth" resultType="java.lang.Double">
        SELECT SUM(rainfall_volume) from irr_inlet_analysis_day WHERE catchment_id=#{catchmentId} AND `day` LIKE CONCAT(#{month},'%')
    </select>

    <select id="getRainCondition" resultType="com.yutoudev.irrigation.irr.controller.large.statistics.vo.RainConditionRespVO">
        SELECT
            ROUND(t.`day`, 1) AS dayRainfall,
            ROUND(t.`month`, 1) AS monthRainfall,
            ROUND(t.`year`, 1) AS yearRainfall,
            ROUND(CASE WHEN (t.`month` - t.qoq_month) = 0 THEN 1 WHEN t.qoq_month = 0 THEN 1 ELSE (t.`month` - t.qoq_month) / t.qoq_month END, 4) * 100 AS monthQOQ,
            ROUND(CASE WHEN (t.`month` - t.yoy_month) = 0 THEN 1 WHEN t.yoy_month = 0 THEN 1 ELSE (t.`month` - t.yoy_month) / t.yoy_month END, 4) * 100 AS monthYOY,
            ROUND(CASE WHEN (t.`year` - t.qoq_year) = 0 THEN 1 WHEN t.qoq_year = 0 THEN 1 ELSE (t.`year` - t.qoq_year) / t.qoq_year END, 4) * 100 AS yearQOQ,
            ROUND(CASE WHEN (t.`year` - t.yoy_year) = 0 THEN 1 WHEN t.yoy_year = 0 THEN 1 ELSE (t.`year` - t.yoy_year) / t.yoy_year END, 4) * 100 AS yearYOY,
            ROUND(mid.rainfall_volume) AS maxDayRainfall,
            eb.st_name AS maxDayRainfallEquipName
        FROM
        (
            SELECT
                COALESCE((SELECT SUM(rainfall_volume) FROM `irr_inlet_analysis_day` WHERE swhs_id = #{swhsId} AND `day` = #{day}), 0) AS `day`,
                COALESCE((SELECT SUM(rainfall_volume) FROM irr_inlet_analysis_month WHERE swhs_id = #{swhsId} AND `month` = #{month}), 0) AS `month`,
                COALESCE((SELECT SUM(rainfall_volume) FROM irr_inlet_analysis_year WHERE swhs_id = #{swhsId} AND `year` = #{year}), 0) AS `year`,
                COALESCE((SELECT SUM(rainfall_volume) FROM `irr_inlet_analysis_day` WHERE swhs_id = #{swhsId} AND `day` &gt;= #{yoyMonthStartTime} AND `day` &lt;= #{yoyMonthEndTime}), 0) AS yoy_month,
                COALESCE((SELECT SUM(rainfall_volume) FROM `irr_inlet_analysis_day` WHERE swhs_id = #{swhsId} AND `day` &gt;= #{qoqMonthStartTime} AND `day` &lt;= #{qoqMonthEndTime}), 0) AS qoq_month,
                COALESCE((SELECT SUM(rainfall_volume) FROM irr_inlet_analysis_day WHERE swhs_id = #{swhsId}  AND `day` &gt;= #{yoyYearStartTime} AND `day` &lt;= #{yoyYearEndTime}), 0) AS yoy_year,
                COALESCE((SELECT SUM(avg_value) FROM irr_history_avg_day WHERE swhs_id = #{swhsId}  AND type=1 AND `code` &gt;= #{qoqYearStartTime} AND `code` &lt;= #{qoqYearEndTime}), 0) AS qoq_year
        ) t
        LEFT JOIN
        (
            SELECT mid.rainfall_volume, mid.equip_id
            FROM irr_meas_ind_day mid
            WHERE mid.equip_id IN (SELECT equip_id FROM irr_catchment_area_equip WHERE catchment_id IN (SELECT id FROM irr_catchment_area WHERE swhs_id = #{swhsId}))
            AND mid.`day` = #{day}
            ORDER BY mid.rainfall_volume DESC
            LIMIT 1
        ) mid ON 1=1
        LEFT JOIN irr_equip_base eb ON eb.id = mid.equip_id
    </select>
</mapper>
