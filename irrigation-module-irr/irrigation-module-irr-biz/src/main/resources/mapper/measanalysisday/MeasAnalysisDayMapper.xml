<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.measanalysisday.MeasAnalysisDayMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into irr_meas_analysis_day(id, catchment_id, `day`, equip_count, avg_rainfall, weighted_avg_rainfall, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.catchmentId}, #{entity.day}, #{entity.equipCount}, #{entity.avgRainfall}, #{entity.weightedAvgRainfall}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        catchment_id = values(catchment_id),
        `day` = values(`day`),
        equip_count = values(equip_count),
        avg_rainfall = values(avg_rainfall),
        weighted_avg_rainfall = values(weighted_avg_rainfall),
        update_time = values(update_time)
    </insert>

    <select id="selectAnalysisListByYear" resultType="com.yutoudev.irrigation.irr.controller.admin.measanalysisday.vo.MeasAnalysisDayBuildYearVO">
        SELECT
        catchment_id,
        period,
        DATE_FORMAT(start_day, '%Y-%m-%d') AS start_day,
        DATE_FORMAT(end_day, '%Y-%m-%d') AS end_day,
        total_rainfall
        FROM
        (
        SELECT
        catchment_id,
        period,
        start_day,
        DATE_ADD(start_day, INTERVAL period - 1 DAY) AS end_day,
        total_rainfall,
        ROW_NUMBER() OVER (PARTITION BY catchment_id, period ORDER BY total_rainfall DESC, start_day) AS rn
        FROM
        (
        SELECT
        r1.catchment_id,
        p.period,
        r1.DATE AS start_day,
        SUM(r2.weighted_avg_rainfall) AS total_rainfall
        FROM
        (
        SELECT
        catchment_id,
        STR_TO_DATE(DAY, '%Y-%m-%d') AS DATE,
        DAY AS day_str
        FROM
        irr_meas_analysis_day
        WHERE
        SUBSTR(DAY, 1, 4) = #{year}
        ) r1
        JOIN (SELECT 3 AS period UNION ALL SELECT 7 AS period UNION ALL SELECT 15 AS period UNION ALL SELECT 30 AS period) p
        JOIN irr_meas_analysis_day r2 ON r2.catchment_id = r1.catchment_id
        AND r2.DAY >= r1.day_str
        AND r2.DAY &lt;= DATE_FORMAT(DATE_ADD(r1.DATE, INTERVAL p.period - 1 DAY), '%Y-%m-%d')
        GROUP BY
        r1.catchment_id,
        r1.DATE,
        p.period
        HAVING
        COUNT(DISTINCT r2.DAY) = p.period
        ) period_sums
        ) ranked
        WHERE
        rn = 1;
    </select>

</mapper>
