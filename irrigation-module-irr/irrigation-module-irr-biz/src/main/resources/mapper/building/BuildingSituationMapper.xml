<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.building.BuildingSituationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectYearStatisticsByBuilding"
            resultType="com.yutoudev.irrigation.irr.controller.large.building.vo.BuildingSituationTypeCountRespVO">
        SELECT situation_type as type,
               CASE situation_type
                   WHEN 40 THEN '大坝'
                   WHEN 20 THEN '渡槽'
                   WHEN 21 THEN '倒虹吸'
                   WHEN 22 THEN '涵洞'
                   WHEN 23 THEN '桥'
                   WHEN 24 THEN '溢流堰'
                   WHEN 25 THEN '隧道'
                   WHEN 26 THEN '泵站'
                   WHEN 27 THEN '闸门'
                   END        as name,
               COUNT(*)       as error_count
        FROM irr_building_situation
        WHERE deleted = 0
          AND situation_type >= 20
          AND handle_status != 2
            AND YEAR(create_time) = YEAR(CURDATE())
        GROUP BY situation_type
        ORDER BY situation_type
    </select>

    <select id="selectYearDangerStats"
            resultType="com.yutoudev.irrigation.irr.controller.large.building.vo.BuildingSituationDangerStatsRespVO">
        SELECT SUM(CASE WHEN situation_type = 10 THEN 1 ELSE 0 END)      as danger_count,
               SUM(CASE WHEN situation_type = 11 THEN amount ELSE 0 END) as dredging_amount,
               SUM(CASE WHEN situation_type = 12 THEN amount ELSE 0 END) as repair_leak_amount
        FROM irr_building_situation
        WHERE deleted = 0
                  AND situation_type &lt; 20
                  AND YEAR (create_time) = YEAR (CURDATE())
    </select>

    <select id="getLeakNum" resultType="java.lang.Long">
        select count(1) leak_num
        from irr_building_situation bs
        <![CDATA[where bs.situation_type = 12
          and bs.handle_status = 2
          and bs.handle_time >= #{startTime}
          and bs.handle_time <= #{endTime};
        ]]>

    </select>
    <select id="getTotalAmount" resultType="java.lang.Float">
        select sum(bs.amount) total_amount
        from irr_building_situation bs
        <![CDATA[where bs.situation_type = 11
          and bs.handle_status = 2
          and bs.handle_time >= #{startTime}
          and bs.handle_time <= #{endTime};
        ]]>
    </select>
</mapper>
