<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.equipbase.EquipBaseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="selectByCentralIdAndDevId"
            resultType="com.yutoudev.irrigation.irr.dal.dataobject.cardbase.CardBaseDO">
        SELECT
            ca.id,
            ca.msisdn,
            ca.iccid,
            ca.imsi,
            ca.active_date,
            ca.open_date,
            ca.amount,
            ca.over_due,
            ca.total_amount,
            ca.use_amount,
            ca.remain_amount,
            ca.status,
            ca.creator,
            ca.create_time,
            ca.updater,
            ca.update_time,
            ca.deleted,
            ca.tenant_id
        FROM
            `irr_equip_base` eq
                INNER JOIN irr_card_base ca ON eq.msisdn = ca.msisdn
        WHERE
            eq.central_id = #{centralId}
          AND eq.dev_id = #{devId}
            LIMIT 1
    </select>

    <select id="selectMeasIndStaticsByIds" resultType="com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindstatistics.MeasIndStatisticsDetailRespVO">
        SELECT
            equip.id equip_id,
            equip.st_name equip_name,
            area.area area
        FROM
            irr_equip_base equip
                LEFT JOIN irr_catchment_area_equip area ON equip.id = area.equip_id
        WHERE
              equip.st_type = 3
          AND equip.dev_type = 3101
          AND equip.deleted = 0
          AND area.equip_id  is NOT NULL
        <if test="ids != null" >
            AND equip.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="equipWaterFlowMonthList"
            resultType="com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipMeasWaterStatisticRespVO">
        SELECT
            ieb.chan_id,
            icb.chan_name,
            irr_meas_ind_day.equip_id,
            (SELECT st_name from irr_equip_base where irr_equip_base.id = irr_meas_ind_day.equip_id) as name,
            '2' as type,
            ROUND(min(min_water_flow)/10000,2) as min,
            ROUND(max(max_water_flow)/10000,2) as max,
            ROUND(sum(water_supply)/10000,2) as avg_or_sum,
            <foreach collection="days" item="day" index="index" separator=",">
                <bind name="_index" value="index+1"/>
                <bind name="_day" value="'day_'+_index"/>
                ROUND(SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = #{day},avg_water_flow/10000,null)),2) as ${_day}
            </foreach>

        FROM
            irr_meas_ind_day LEFT JOIN irr_chan_equip_query  ieb on ieb.equip_id = irr_meas_ind_day.equip_id  LEFT JOIN irr_chan_base icb on icb.id = ieb.chan_id where 1=1 and
            EXISTS (SELECT 1 from irr_equip_base where st_type = 1 and (dev_type = 1201 or dev_type = 1202 or dev_type = 1203) and irr_meas_ind_day.equip_id = irr_equip_base.id)
                               and day BETWEEN #{startDate} and #{endDate}
        GROUP BY
        irr_meas_ind_day.equip_id
        ORDER BY
        irr_meas_ind_day.equip_id

    </select>
    <select id="equipWaterLevelMonthList"
            resultType="com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipMeasWaterStatisticRespVO">
        SELECT
            ieb.chan_id,
        icb.chan_name,
        irr_meas_ind_day.equip_id,
            (SELECT st_name from irr_equip_base where irr_equip_base.id = irr_meas_ind_day.equip_id) as name,
            '1' as type,
            ROUND(min(min_water_level),2) as min,
            ROUND(max(max_water_level),2) as max,
            ROUND(avg(avg_water_level),2) as avg_or_sum,
            <foreach collection="days" item="day" index="index" separator=",">
                <bind name="_index" value="index+1"/>
                <bind name="_day" value="'day_'+_index"/>
                ROUND(SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = #{day},avg_water_level,null)),2) as ${_day}
            </foreach>
        FROM
            irr_meas_ind_day LEFT JOIN irr_chan_equip_query  ieb on ieb.equip_id = irr_meas_ind_day.equip_id  LEFT JOIN irr_chan_base icb on icb.id = ieb.chan_id where 1=1 and
            EXISTS (SELECT 1 from irr_equip_base where st_type = 1 and (dev_type = 1201 or dev_type = 1202 or dev_type = 1203) and irr_meas_ind_day.equip_id = irr_equip_base.id)
                               and day BETWEEN #{startDate} and #{endDate}
        GROUP BY
        irr_meas_ind_day.equip_id
        ORDER BY
        irr_meas_ind_day.equip_id
    </select>

    <select id="equipWaterFlowYearList"
            resultType="com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipMeasWaterStatisticRespVO">
        SELECT
        ieb.chan_id,
        icb.chan_name,
        irr_meas_ind_day.equip_id,
        (SELECT st_name from irr_equip_base where irr_equip_base.id = irr_meas_ind_day.equip_id) as name,
        '2' as type,
        ROUND(min(min_water_flow)/10000,2) as min,
        ROUND(max(max_water_flow)/10000,2) as max,
        ROUND(avg(max_water_flow)/10000,2) as avg_or_sum,
        <foreach collection="days" item="day" index="index" separator=",">
            <bind name="_index" value="index+1"/>
            <bind name="_day" value="'day_'+_index"/>
            ROUND(avg(IF(DATE_FORMAT(irr_meas_ind_day.day, '%m') = #{day},avg_water_flow/10000,null)),2) as ${_day}
        </foreach>

        FROM
        irr_meas_ind_day LEFT JOIN irr_chan_equip_query  ieb on ieb.equip_id = irr_meas_ind_day.equip_id  LEFT JOIN irr_chan_base icb on icb.id = ieb.chan_id where 1=1 and
        EXISTS (SELECT 1 from irr_equip_base where st_type = 1 and (dev_type = 1201 or dev_type = 1202 or dev_type = 1203) and irr_meas_ind_day.equip_id = irr_equip_base.id)
        and day BETWEEN #{startDate} and #{endDate}
        GROUP BY
        irr_meas_ind_day.equip_id
        ORDER BY
        irr_meas_ind_day.equip_id

    </select>

    <select id="equipWaterLevelYearList"
            resultType="com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipMeasWaterStatisticRespVO">
        SELECT
        ieb.chan_id,
        icb.chan_name,
        irr_meas_ind_day.equip_id,
        (SELECT st_name from irr_equip_base where irr_equip_base.id = irr_meas_ind_day.equip_id) as name,
        '1' as type,
        ROUND(min(avg_water_level),2) as min,
        ROUND(max(avg_water_level),2) as max,
        ROUND(avg(avg_water_level),2) as avg_or_sum,
        <foreach collection="days" item="day" index="index" separator=",">
            <bind name="_index" value="index+1"/>
            <bind name="_day" value="'day_'+_index"/>
            ROUND(avg(IF(DATE_FORMAT(irr_meas_ind_day.day, '%m') = #{day},avg_water_level,null)),2) as ${_day}
        </foreach>
        FROM
        irr_meas_ind_day LEFT JOIN irr_chan_equip_query  ieb on ieb.equip_id = irr_meas_ind_day.equip_id  LEFT JOIN irr_chan_base icb on icb.id = ieb.chan_id where 1=1 and
        EXISTS (SELECT 1 from irr_equip_base where st_type = 1 and (dev_type = 1201 or dev_type = 1202 or dev_type = 1203) and irr_meas_ind_day.equip_id = irr_equip_base.id)
        and day BETWEEN #{startDate} and #{endDate}
        GROUP BY
        irr_meas_ind_day.equip_id
        ORDER BY
        irr_meas_ind_day.equip_id
    </select>

    <select id="selectByByChanBseIds" resultType="com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ThematicMapEquipVO">
        select
        ieb.id,
        ieb.central_id ,
        ice.chan_id ,
        ice.equip_id ,
        ieb.group_type ,
        st_type,
        ieb.dev_id ,
        dev_type,
        ieb.dpds_num as mlg_num,
        ieb.st_code,
        ieb.st_name
        from
            irr_chan_equip ice
        left join irr_equip_base ieb on ice.equip_id = ieb.id
        where
            ice.chan_id in
            <foreach collection="chanBaseIds" item="chanBaseId" open="(" separator="," close=")">
                #{chanBaseId}
            </foreach>
    </select>

    <select id="getGateCameraByAllocationWaterList" resultType="com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO">
        SELECT
            b.*
        FROM
            irr_equip_base b
                JOIN irr_equip_gate_camera gc ON b.id = gc.camera_id
                JOIN ( SELECT DISTINCT gate_id FROM irr_allocation_water_scheme WHERE end_time > NOW() ) aws ON gc.gate_id = aws.gate_id
    </select>

    <select id="statsOnline" resultType="com.yutoudev.irrigation.irr.controller.large.equip.vo.EquipOnlineStatsRespVO">
        SELECT
            st_type ,
            COUNT(*) as total,
            SUM(CASE WHEN online = 1 THEN 1 ELSE 0 END) as online_count,
            SUM(CASE WHEN online = 0 THEN 1 ELSE 0 END) as offline_count
        FROM
            irr_equip_base
        WHERE
            deleted = 0
        GROUP BY
            st_type
    </select>

    <select id="getEquipByChanId" resultType="com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO">
        SELECT
            eb.*
        FROM
            irr_chan_equip ce
                LEFT JOIN irr_equip_base eb ON eb.id = ce.equip_id
        WHERE
            ce.chan_id = #{chanId}
          AND eb.dpds_num != ''
	      AND eb.dev_type = 7103
        ORDER BY
            eb.dpds_num DESC
    </select>

    <select id="internetOfThings" resultType="com.yutoudev.irrigation.irr.controller.admin.equipcontrol.vo.InternetOfThingsRespVO" parameterType="com.yutoudev.irrigation.irr.controller.admin.equipcontrol.vo.InternetOfThingsReqVO">
        SELECT
            ieb.st_name,
            ieb.`online`,
            ieb.central_id,
            ieb.dev_id,
            ieb.rtu_model,
            ieb.sensor_model,
            ieb.protocol,
            ieb.msisdn,
            icb.use_amount
        FROM
            irr_equip_base ieb
                LEFT JOIN irr_card_base icb ON icb.msisdn = ieb.msisdn
        <where>
            ieb.deleted = 0
            <if test="req.stType != null and req.stType != ''"> and ieb.st_type = #{req.stType}</if>
        </where>
    </select>


    <select id="equipWaterCountYearList"
            resultType="com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipMeasWaterStatisticRespVO">
        SELECT
        ieb.chan_id,
        icb.chan_name,
        irr_meas_ind_day.equip_id,
        (SELECT st_name from irr_equip_base where irr_equip_base.id = irr_meas_ind_day.equip_id) as name,
        '3' as type,
        ROUND(min(min_water_flow)/10000,2) as min,
        ROUND(max(max_water_flow)/10000,2) as max,
        ROUND(sum(max_water_flow)/10000,2) as avg_or_sum,
        <foreach collection="days" item="day" index="index" separator=",">
            <bind name="_index" value="index+1"/>
            <bind name="_day" value="'day_'+_index"/>
            ROUND(SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%m') = #{day},water_supply/10000,null)),2) as ${_day}
        </foreach>

        FROM
        irr_meas_ind_day LEFT JOIN irr_chan_equip_query  ieb on ieb.equip_id = irr_meas_ind_day.equip_id  LEFT JOIN irr_chan_base icb on icb.id = ieb.chan_id where 1=1 and
        EXISTS (SELECT 1 from irr_equip_base where st_type = 1 and (dev_type = 1201 or dev_type = 1202 or dev_type = 1203) and irr_meas_ind_day.equip_id = irr_equip_base.id)
        and day BETWEEN #{startDate} and #{endDate}
        GROUP BY
        irr_meas_ind_day.equip_id
        ORDER BY
        irr_meas_ind_day.equip_id
    </select>

    <select id="equipWaterCountMonthList"
            resultType="com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipMeasWaterStatisticRespVO">
        SELECT
        ieb.chan_id,
        icb.chan_name,
        irr_meas_ind_day.equip_id,
        (SELECT st_name from irr_equip_base where irr_equip_base.id = irr_meas_ind_day.equip_id) as name,
        '3' as type,
        ROUND(min(water_supply)/10000,2) as min,
        ROUND(max(water_supply)/10000,2) as max,
        ROUND(sum(water_supply)/10000,2) as avg_or_sum,
        <foreach collection="days" item="day" index="index" separator=",">
            <bind name="_index" value="index+1"/>
            <bind name="_day" value="'day_'+_index"/>
            ROUND(SUM(IF(DATE_FORMAT(irr_meas_ind_day.day, '%d') = #{day},water_supply/10000,null)),2) as ${_day}
        </foreach>

        FROM
        irr_meas_ind_day LEFT JOIN irr_chan_equip_query  ieb on ieb.equip_id = irr_meas_ind_day.equip_id  LEFT JOIN irr_chan_base icb on icb.id = ieb.chan_id where 1=1 and
        EXISTS (SELECT 1 from irr_equip_base where st_type = 1 and (dev_type = 1201 or dev_type = 1202 or dev_type = 1203) and irr_meas_ind_day.equip_id = irr_equip_base.id)
        and day BETWEEN #{startDate} and #{endDate}
        GROUP BY
        irr_meas_ind_day.equip_id
        ORDER BY
        irr_meas_ind_day.equip_id

    </select>
    <select id="listCameraSiteChanInfo"
            resultType="com.yutoudev.irrigation.irr.controller.app.equip.vo.CameraSiteRespVO">

        SELECT
            equip.id as id,
            chan.id AS chan_id,
            chan.chan_name AS chan_name,
            chan.parent_id AS parent_id,
            chan.parent_tree_id AS parent_tree_id,
            chan.swhs_id AS swhs_id,
            chan.max_warning_value AS max_warning_value,
            chan.chan_type AS chan_type,
            chan.mlg_num AS chan_mlg_num,
            chan.sort AS chan_sort,
            equip.id as equip_id,
            equip.st_name AS st_name,
            equip.pic_url AS pic_url,
            equip.`online` AS `online`,
            equip.take_photo AS take_photo,
            equip.protocol AS protocol,
            equip.sort AS equip_sort,
            equip.dpds_num AS equip_mlg_num,
            equip.camera_uid AS camera_uid,
            equip.camera_channel AS camera_channel,
            equip.preview_time AS preview_time,
            equip.tags AS tags
        FROM
          irr_equip_base AS equip
          LEFT JOIN ( SELECT equip_id, chan_id FROM irr_chan_equip GROUP BY equip_id ) AS chanEquip ON chanEquip.equip_id = equip.id
          LEFT JOIN irr_chan_base AS chan ON chan.id = chanEquip.chan_id
        WHERE
            equip.deleted = 0 AND equip.st_type = 2
    </select>
    <select id="pageEquipGisSimpleByGis"
            resultType="com.yutoudev.irrigation.irr.controller.app.equip.vo.EquipGisSimpleRespVO">
        SELECT
            id,
            st_type,
            st_name,
            dev_type,
            central_id,
            dev_id,
            st_lng,
            st_lat,
            st_loc,
            ROUND(6371 * 2 * ASIN(SQRT(
                POWER(SIN((#{lat} - st_lat) * PI() / 180 / 2), 2) +
                COS(#{lat} * PI() / 180) * COS(st_lat * PI() / 180) *
                POWER(SIN((#{lng} - st_lng) * PI() / 180 / 2), 2)
            )), 2) AS distance
        FROM
            irr_equip_base
        WHERE
            deleted = 0 AND st_lng IS NOT NULL AND st_lat IS NOT NULL
        ORDER BY
            distance ASC;
    </select>
</mapper>
